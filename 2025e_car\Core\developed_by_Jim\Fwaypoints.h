/**
 * @file Fwaypoints.h
 * @brief 飞机航点生成系统头文件 - 智能优化版本
 * <AUTHOR>
 * @date 2025
 *
 * 功能：基于网格地图生成优化的无人机航点
 * 策略：无障碍行只生成起点终点，有障碍行生成详细绕行航点
 * 输出：无人机坐标系的实际坐标
 */

#ifndef __FWAYPOINTS_H
#define __FWAYPOINTS_H

#include "board.h"
#include <stdint.h>

// 无人机坐标系参数（基于450cm×350cm场地，63个50cm×50cm方格）
#define DRONE_GRID_SIZE_CM 50.0f     // 每格实际尺寸50cm
#define DRONE_ORIGIN_X 0.0f          // 无人机坐标系原点X坐标
#define DRONE_ORIGIN_Y 0.0f          // 无人机坐标系原点Y坐标

// 航点系统参数
#define MAX_FLIGHT_WAYPOINTS 50      // 最大航点数量

// 网格系统常量（与2025E.h保持一致）
#define GRID_ROWS 7    // 网格行数(B1-B7)
#define GRID_COLS 9    // 网格列数(A1-A9)

// 航点类型定义
typedef enum {
    WAYPOINT_NORMAL = 0,     // 正常航点
    WAYPOINT_BYPASS = 1,     // 绕行航点
    WAYPOINT_TRANSITION = 2  // 行间转换航点
} WaypointType_t;

// 无人机航点结构体
typedef struct {
    float x, y;              // 无人机坐标系坐标
    WaypointType_t type;     // 航点类型
    uint8_t grid_row;        // 对应网格行号（调试用）
    uint8_t grid_col;        // 对应网格列号（调试用）
} DroneWaypoint_t;

// 行分析结果结构体
typedef struct {
    uint8_t has_obstacles;       // 是否有障碍物
    uint8_t obstacle_count;      // 障碍物数量
    uint8_t first_obstacle_col;  // 第一个障碍物列号
    uint8_t last_obstacle_col;   // 最后一个障碍物列号
} RowAnalysis_t;
/**
 * @brief 列分析结果结构体
 */
typedef struct {
    uint8_t has_obstacles;        // 是否有障碍物
    uint8_t first_obstacle_row;   // 第一个障碍物行号
    uint8_t last_obstacle_row;    // 最后一个障碍物行号
    uint8_t obstacle_count;       // 障碍物数量
} ColAnalysis_t;

// 外部函数声明
extern void HAL_Delay(uint32_t Delay);

// 从2025E.c引用的外部函数
extern uint8_t IsGridAccessible(uint8_t row, uint8_t col);
extern void InitGridMap(void);
extern void SetNoFlyZone(uint8_t row, uint8_t col);
extern      DroneWaypoint_t test_waypoints[MAX_FLIGHT_WAYPOINTS];
extern  uint16_t waypoint_count;
// 主要函数声明

/**
 * @brief 生成优化的横向巡航航点（主函数）
 * @param waypoints 输出航点数组
 * @param max_count 最大航点数
 * @return 生成的航点数量
 */
uint16_t GenerateOptimizedHorizontalWaypoints(DroneWaypoint_t *waypoints, uint16_t max_count);

/**
 * @brief 横向巡航航点生成测试函数
 */
void TestHorizontalWaypointGeneration(void);

/**
 * @brief 简单测试函数 - 用于排查问题
 */



 void SimpleWaypointTest(void);

/**
 * @brief 最小化测试函数 - 解决OLED显示问题
 */
void MinimalWaypointTest(void);
void TestVerticalWaypointGeneration(void);

/**
 * @brief 检测禁飞区方向，用于智能模式切换
 * @return 0=横向禁飞区(使用横向扫描), 1=纵向禁飞区(使用纵向扫描)
 */
uint8_t DetectNoFlyZoneOrientation(void);

/**
 * @brief 生成优化的纵向巡航航点（主函数）
 * @param waypoints 输出航点数组
 * @param max_count 最大航点数
 * @return 生成的航点数量
 */
uint16_t GenerateOptimizedVerticalWaypoints(DroneWaypoint_t *waypoints, uint16_t max_count);

#endif