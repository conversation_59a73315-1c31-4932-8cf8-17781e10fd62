/* ----------------------------------------------------------------------
 * Project:      CMSIS DSP Library
 * Title:        BasicMathFunctionsF16.c
 * Description:  Combination of all basic math function f16 source files.
 *
 * $Date:        20. April 2020
 * $Revision:    V1.1.0
 *
 * Target Processor: Cortex-M cores
 * -------------------------------------------------------------------- */
/*
 * Copyright (C) 2019-2020 ARM Limited or its affiliates. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "arm_abs_f16.c"
#include "arm_add_f16.c"
#include "arm_dot_prod_f16.c"
#include "arm_mult_f16.c"
#include "arm_negate_f16.c"
#include "arm_offset_f16.c"
#include "arm_scale_f16.c"
#include "arm_sub_f16.c"
#include "arm_clip_f16.c"
