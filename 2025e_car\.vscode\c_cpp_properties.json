{"configurations": [{"name": "STM32F4", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/Core/Inc", "${workspaceFolder}/Core/Src", "${workspaceFolder}/Drivers/STM32F4xx_HAL_Driver/Inc", "${workspaceFolder}/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "${workspaceFolder}/Drivers/CMSIS/Device/ST/STM32F4xx/Include", "${workspaceFolder}/Drivers/CMSIS/Include", "${workspaceFolder}/Core/developed_by_<PERSON>"], "defines": ["USE_HAL_DRIVER", "STM32F407xx", "DEBUG"], "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "msvc-x64", "compilerPath": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe", "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}