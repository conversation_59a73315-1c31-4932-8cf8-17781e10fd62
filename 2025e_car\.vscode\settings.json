{"files.associations": {"board.h": "c", "stm32f4xx_hal.h": "c", "mytimer.h": "c", "tim.h": "c", "motor_pwm.h": "c", "stdio.h": "c", "main.h": "c", "encoder.h": "c", "m_pid.h": "c", "jy61p.h": "c", "usart.h": "c", "m_usart.h": "c", "m_navy.h": "c", "dma.h": "c", "mission.h": "c", "tjc_usart_hmi.h": "c", "2025e.h": "c", "fwaypoints.h": "c", "oled.h": "c", "waypoint.h": "c", "key.h": "c"}, "cmake.sourceDirectory": "G:/2025E/2025e_car/Drivers/CMSIS/DSP/Source/DistanceFunctions", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.default.includePath": ["${workspaceFolder}/**", "${workspaceFolder}/Core/Inc", "${workspaceFolder}/Drivers/STM32F4xx_HAL_Driver/Inc", "${workspaceFolder}/Drivers/CMSIS/Device/ST/STM32F4xx/Include", "${workspaceFolder}/Drivers/CMSIS/Include"], "C_Cpp.default.defines": ["USE_HAL_DRIVER", "STM32F407xx"], "C_Cpp.default.cStandard": "c11", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.intelliSenseMode": "msvc-x64"}