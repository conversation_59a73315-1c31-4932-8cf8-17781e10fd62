#ifndef __M_USART_H
#define __M_USART_H

#include "board.h"
extern uint8_t USART3_RxData;

void Uart_Init(void);
void send_firewater_data(void);
void Process_Drone_Data(void); // 处理飞机数据函数

// 简单的路径生成控制标志
extern uint8_t path_generation_enabled;
extern uint32_t last_path_gen_time;

typedef struct
{
    float drone_x;
    float drone_y;
    uint8_t fire_id;

} drone_data_t;

// 动物数据结构体
typedef struct
{
    int species_count;     // 种类数量
    char grid_position[6]; // 网格位置字符串，如"A3B5"
} AnimalData;

// 禁飞区管理结构
typedef struct
{
    uint8_t count;   // 当前接收到的禁飞区数量 (0-3)
    uint8_t row;     // 禁飞区所在行号 (0-6对应B1-B7)
    uint8_t cols[3]; // 三个禁飞区的列号 (0-8对应A1-A9)
} NoFlyZoneManager_t;
typedef struct
{
    float x;     // 飞机坐标系X
    float y;     // 飞机坐标系Y
    uint8_t num; // 禁飞区序号
} NoFlyZone_t;

extern NoFlyZone_t nfz_data[3];
extern drone_data_t drone_data;
extern NoFlyZoneManager_t nfz_manager;
extern AnimalData animal_data; // 动物数据全局变量
extern char hang[3];
extern char lie[3];
extern uint8_t row;
extern uint8_t col;
extern uint8_t dis_counter;
extern uint8_t nfz_received_count;

// 简单的控制函数
void SetPathGenerationEnabled(uint8_t enable);
void ResetNoFlyZoneData(void);

#endif