任务需求：在串口屏上实现飞机巡航路径显示
串口屏画线指令格式 ："line %d,%d,%d,%d,%d\xFF\xFF\xFF"
地图环境：9×7网格（A1-A9, B1-B7），总共63个格子
约束条件：

有3个格子的禁飞区（绿色矩形区域）
需要巡航剩余的60个格子
起点在右下角绿色点
遍历巡航问题，要访问所有可访问的格子

避让规则：

遇到禁飞区默认向下绕行
特殊情况：禁飞区在最下面B1行时，向上绕行

技术参数：

地图尺寸：720×560像素
网格规格：每个格子80×80像素
坐标系：左上角原点，右下为正方向
 巡航策略:蛇形扫描法
 现在只考虑横向巡航  假设禁飞区都是横向的三个连续格子。
 我的思路：我有一个办法，我把地图尺寸，和方块尺寸告诉你，然后封装成一个函数，把64个方格的坐标封装好。
 这样路径规划就变成了，选方格，遇到禁飞区就向下飞一个方格然后再回到原来的行


让我详细描述飞机坐标系统和网格坐标的关系：
坐标系统详细说明
1. 地图基本信息
实际地图尺寸：450cm × 350cm
网格划分：9×7网格
每个格子尺寸：50cm × 50cm（450÷9=50, 350÷7=50）
2. 网格坐标系统
A轴（水平）：A1到A9（从左到右，9个格子）
B轴（垂直）：B1到B7（从下到上，7个格子）
网格原点：左下角为A1B1
3. 飞机坐标系统
飞机起飞点：右下角（对应网格A9B1）
飞机坐标原点：(0, 0) = A9B1位置
X轴方向：向左为负值，向右为正值
Y轴方向：向上为正值，向下为负值

// 飞机坐标 → 网格坐标
grid_a = 9 + (int)(x / 50.0f);  // A坐标
grid_b = (int)(y / 50.0f) + 1;  // B坐标


(-400,300) ---- ... ---- (0,300)
    |                       |
    |                       |
(-400,250) ---- ... ---- (0,250)
    |                       |
    .                       .
    .                       .
    |                       |
(-400,0) ------ ... ---- (0,0) ← 飞机起飞点

🎯 完整的处理逻辑
偶数行（从右到左A9→A1）
检查A1B7特殊情况 → 如果是则停止
检查是否超出右边界 → 如果是则调整绕行起点
执行三段式绕行 → 垂直→水平→垂直
继续巡航到A1
奇数行（从左到右A1→A9）
无特殊停止条件（因为终点是A1B7，不在奇数行）
检查是否超出左边界 → 如果是则调整绕行起点
执行三段式绕行 → 垂直→水平→垂直
继续巡航到A9
B1行特殊处理
绕行方向：向上绕行到B2行（因为B1是最底部）
其他逻辑：与普通行相同
🚀 关键原则
最小化停止：只有真正无法到达最终目标时才停止
最大化覆盖：尽可能覆盖所有可访问区域
边界安全：所有航点都在有效范围内
路径连续：绕行后能正确回到原路径继续
📊 正确效果
A1B7禁飞区：直接停止 ✅
其他所有禁飞区：正常绕行 ✅
边缘禁飞区：智能绕行，不超出边界 ✅
完全覆盖：除禁飞区外的所有格子都被访问 ✅
这就是正确的航点禁飞区处理办法：特殊位置特殊处理，其他位置统一绕行。🎯 特殊情况处理规则
偶数行（B1,B3,B5,B7）最左边禁飞区
条件：偶数行 + 禁飞区包含A1（analysis.first_obstacle_col == 0）
处理：向上绕行，两段式
路径：当前行禁飞区前 → 向上一行 → 水平到A1 → 继续完成上一行
例外：B7行A1禁飞区直接停止（最终终点）
奇数行（B2,B4,B6）最右边禁飞区
条件：奇数行 + 禁飞区包含A9（analysis.last_obstacle_col == GRID_COLS-1）
处理：向上绕行，两段式
路径：当前行禁飞区前 → 向上一行 → 水平到A9 → 继续完成上一行


📋 飞机航点生成系统 - 所有特殊情况总结
🎯 特殊情况1：A1B7终点禁飞区
条件：B7行（Row=6）且A1位置是禁飞区（analysis.first_obstacle_col == 0）
处理：直接停止，不进行任何绕行
原因：A1B7是整个巡航的最终终点，无法到达就提前结束
代码位置：偶数行处理逻辑中的特殊检查
🎯 特殊情况2：偶数行最左边禁飞区（两段式绕行）
条件：偶数行（B1,B3,B5）且禁飞区包含A1（analysis.first_obstacle_col == 0）
处理：向上绕行，两段式，不回到原行
路径：当前行禁飞区前 → 向上一行 → 水平到A1
优势：避免三段式复杂性，路径更自然
例外：B7行A1禁飞区按特殊情况1处理
🎯 特殊情况3：奇数行最右边禁飞区（两段式绕行）
条件：奇数行（B2,B4,B6）且禁飞区包含A9（analysis.last_obstacle_col == GRID_COLS-1）
处理：向上绕行，两段式，不回到原行
路径：当前行禁飞区前 → 向上一行 → 水平到A9
优势：避免斜飞穿越禁飞区问题
🎯 特殊情况4：B1行绕行方向
条件：B1行（Row=0）遇到禁飞区
处理：向上绕行到B2行（因为B1是最底部）
原因：B1行无法向下绕行
🎯 特殊情况5：右边界禁飞区边界检查
条件：禁飞区延伸到右边缘A9，绕行起点超出地图
处理：跳过超出边界的绕行起点，直接从禁飞区左边开始绕行
防止：生成Col=9等超出地图范围的航点
🎯 特殊情况6：重复航点消除
条件：行间转换航点与下一行起点坐标相同
处理：检测重复后跳过转换航点生成
优化：减少冗余航点，提高路径效率
🔄 正常情况：三段式绕行
适用：除上述特殊情况外的所有禁飞区
流程：垂直移动到绕行行 → 水平跳过禁飞区 → 垂直回到原行继续
方向：B1行向上绕行，其他行向下绕行
📊 特殊情况优先级
A1B7终点检查（最高优先级）
边缘禁飞区两段式绕行
边界安全检查
正常三段式绕行
重复航点消除