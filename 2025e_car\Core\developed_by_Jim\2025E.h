/**
 * @file 2025E.h
 * @brief 飞机巡航路径规划系统头文件
 * <AUTHOR>
 * @date 2025
 *
 * 功能：在串口屏上实现飞机巡航路径显示
 * 地图：9×7网格（A1-A9, B1-B7），720×560像素，每格80×80像素
 * 策略：蛇形扫描法，遇到禁飞区向下绕行
 */

#ifndef __2025E_H
#define __2025E_H

#include "board.h"
#include "2025E.h"

// 外部函数声明
extern void HAL_Delay(uint32_t Delay);

// 颜色定义
#define COLOR_RED       0xF800      // 红色（绕行路径）
#define COLOR_GREEN     0x07E0      // 绿色（正常路径）
#define COLOR_BLUE      0x001F      // 蓝色
#define COLOR_WHITE     0xFFFF      // 白色
#define COLOR_BLACK     0x0000      // 黑色

// 函数声明

/**
 * @brief 初始化网格地图坐标
 * @note 按照你的思路：把64个方格的坐标封装好
 */
void InitGridMap(void);

/**
 * @brief 设置禁飞区（横向3个连续格子）
 * @param row 行号 (0-6)
 * @param start_col 起始列号 (0-6，确保不超出边界)
 */
void SetNoFlyZone(uint8_t row, uint8_t start_col);

/**
 * @brief 蛇形扫描巡航路径生成（核心算法）
 * @note 实现你的思路：路径规划变成选方格，遇到禁飞区就绕行
 * @return 生成的路径段数量
 */
uint16_t GenerateSnakeCruisePath(void);

/**
 * @brief 设置显示模式
 * @param mode 0=正常模式(有延时), 1=快速模式(无延时)
 */
void SetDisplayMode(uint8_t mode);

/**
 * @brief 巡航系统测试函数
 * @note 完整的测试流程，演示你的思路实现
 */
void CruiseSystemTest(void);

/**
 * @brief 快速巡航测试（无延时，直接画线）
 */
void FastCruiseTest(void);

/**
 * @brief 纵向蛇形扫描巡航路径生成（处理纵向禁飞区）
 * @note 按列扫描，从A9B1开始向上，遇到纵向禁飞区进行水平绕行
 * @return 生成的路径段数量
 */
uint16_t GenerateVerticalSnakeCruisePath(void);

/**
 * @brief 设置纵向禁飞区（三个连续格子）
 * @param start_row 起始行号 (0-4，确保有足够空间设置3个格子)
 * @param col 列号 (0-8)
 */
void SetVerticalNoFlyZone(uint8_t start_row, uint8_t col);

/**
 * @brief 纵向禁飞区巡航系统测试函数
 * @note 测试纵向三个连续禁飞区的处理
 */
void VerticalCruiseSystemTest(void);

/**
 * @brief 获取指定格子的坐标
 * @param row 行号 (0-6)
 * @param col 列号 (0-8)
 * @param x 返回X坐标
 * @param y 返回Y坐标
 * @return 1:成功, 0:失败
 */
uint8_t GetGridCoordinate(uint8_t row, uint8_t col, uint16_t *x, uint16_t *y);
void ConvertCoord(char* lie_str, char* hang_str, uint8_t* row, uint8_t* col);
void ExecuteCruisePathDisplay(void);
void QuickVerticalTest(void);

// OLED调试函数声明
void OLED_ClearDebug(void);
void OLED_DebugMsg(char *msg);
void OLED_DebugNum(char *msg, uint32_t num);
void OLED_DebugCoord(char *name, uint16_t x, uint16_t y);

#endif