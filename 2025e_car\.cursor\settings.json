{"editor.gotoLocation.multipleDefinitions": "goto", "editor.gotoLocation.multipleDeclarations": "goto", "editor.gotoLocation.multipleImplementations": "goto", "editor.gotoLocation.multipleReferences": "goto", "editor.gotoLocation.multipleTypeDefinitions": "goto", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.enhancedColorization": "enabled", "C_Cpp.default.browse.path": ["${workspaceFolder}/**", "${workspaceFolder}/Core/Inc", "${workspaceFolder}/Core/Src", "${workspaceFolder}/Drivers/STM32F4xx_HAL_Driver/Inc", "${workspaceFolder}/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "${workspaceFolder}/Drivers/CMSIS/Device/ST/STM32F4xx/Include", "${workspaceFolder}/Drivers/CMSIS/Include", "${workspaceFolder}/Core/developed_by_<PERSON>"], "C_Cpp.default.includePath": ["${workspaceFolder}/**", "${workspaceFolder}/Core/Inc", "${workspaceFolder}/Core/Src", "${workspaceFolder}/Drivers/STM32F4xx_HAL_Driver/Inc", "${workspaceFolder}/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "${workspaceFolder}/Drivers/CMSIS/Device/ST/STM32F4xx/Include", "${workspaceFolder}/Drivers/CMSIS/Include", "${workspaceFolder}/Core/developed_by_<PERSON>"], "C_Cpp.default.defines": ["USE_HAL_DRIVER", "STM32F407xx", "DEBUG"], "C_Cpp.default.cStandard": "c11", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.intelliSenseMode": "msvc-x64", "editor.mouseNavigationEnabled": true, "editor.definitionLinkOpensInPeek": false}